/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #0a0a0a;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    padding: 20px 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 18px;
}

.logo-icon {
    font-size: 20px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 40px;
}

.nav-menu a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 400;
    font-size: 16px;
    transition: opacity 0.3s ease;
}

.nav-menu a:hover {
    opacity: 0.8;
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)), url('images/hero.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
}

.hero-content {
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-size: 72px;
    font-weight: 600;
    line-height: 1.1;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.hero-title em {
    font-style: italic;
    font-weight: 300;
}

.hero-subtitle {
    font-size: 18px;
    font-weight: 400;
    line-height: 1.6;
    margin-bottom: 40px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.btn {
    padding: 16px 32px;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    border: none;
    font-family: inherit;
}

.btn-primary {
    background: #ffffff;
    color: #000000;
}

.btn-primary:hover {
    background: #f0f0f0;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    border-color: rgba(255, 255, 255, 0.6);
    transform: translateY(-2px);
}

.play-icon {
    font-size: 12px;
}

.scroll-indicator {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
}

.scroll-arrow {
    font-size: 24px;
    opacity: 0.7;
    animation: bounce 2s infinite;
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-10px);
    }

    60% {
        transform: translateY(-5px);
    }
}

/* Features Section */
.features {
    padding: 120px 0;
    background: #0a0a0a;
}

.section-title {
    font-size: 48px;
    font-weight: 600;
    line-height: 1.2;
    text-align: center;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.section-title em {
    font-style: italic;
    font-weight: 300;
}

.section-subtitle {
    font-size: 18px;
    font-weight: 400;
    line-height: 1.6;
    text-align: center;
    opacity: 0.8;
    margin-bottom: 80px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-bottom: 60px;
}

.feature-card {
    background: #111111;
    border-radius: 16px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px);
}

.feature-image {
    width: 100%;
    height: 240px;
    overflow: hidden;
}

.feature-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Placeholder images for feature cards */
.feature-card:nth-child(1) .feature-image {
    background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.feature-card:nth-child(1) .feature-image::before {
    content: "▶";
    font-size: 48px;
    color: white;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-card:nth-child(2) .feature-image {
    background: linear-gradient(135deg, #4A90E2 0%, #7B68EE 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.feature-card:nth-child(2) .feature-image::before {
    content: "#";
    font-size: 48px;
    color: white;
    font-weight: bold;
}

.feature-card:nth-child(3) .feature-image {
    background: linear-gradient(135deg, #E74C3C 0%, #F39C12 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.feature-card:nth-child(3) .feature-image::before {
    content: "📄";
    font-size: 48px;
}

.feature-card h3 {
    font-size: 24px;
    font-weight: 600;
    margin: 24px 24px 12px;
}

.feature-card p {
    font-size: 16px;
    opacity: 0.8;
    margin: 0 24px 20px;
    line-height: 1.5;
}

.read-more {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    margin: 0 24px 24px;
    display: inline-block;
    transition: opacity 0.3s ease;
}

.read-more:hover {
    opacity: 0.8;
}

.explore-all {
    text-align: center;
}

.btn-outline {
    background: transparent;
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.btn-outline:hover {
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
}

/* Pricing Section */
.pricing {
    padding: 120px 0;
    background: #0a0a0a;
}

.pricing-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin-bottom: 60px;
    font-size: 16px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #333;
    transition: 0.4s;
    border-radius: 30px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked+.slider {
    background-color: #ffffff;
}

input:checked+.slider:before {
    transform: translateX(30px);
    background-color: #000;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: #111111;
    border: 2px solid transparent;
    border-radius: 16px;
    padding: 40px 32px;
    text-align: center;
    transition: all 0.3s ease;
}

.pricing-card.featured {
    border-color: #ffffff;
    transform: scale(1.05);
}

.pricing-card:hover {
    transform: translateY(-8px);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-8px);
}

.pricing-card h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
}

.price {
    margin-bottom: 32px;
}

.currency {
    font-size: 24px;
    font-weight: 600;
    vertical-align: top;
}

.amount {
    font-size: 48px;
    font-weight: 700;
}

.period {
    font-size: 16px;
    opacity: 0.7;
    display: block;
    margin-top: 4px;
}

.features-list {
    list-style: none;
    margin-bottom: 32px;
    text-align: left;
}

.features-list li {
    padding: 8px 0;
    font-size: 16px;
}

/* FAQ Section */
.faq {
    padding: 120px 0;
    background: #0a0a0a;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 60px;
    max-width: 1000px;
    margin: 0 auto;
}

.faq-item {
    margin-bottom: 40px;
}

.faq-item h4 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 16px;
    line-height: 1.4;
}

.faq-item p {
    font-size: 16px;
    line-height: 1.6;
    opacity: 0.8;
}

/* Footer */
.footer {
    padding: 60px 0;
    background: #0a0a0a;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 40px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 18px;
}

.footer-nav {
    display: flex;
    gap: 32px;
}

.footer-nav a {
    color: #ffffff;
    text-decoration: none;
    font-size: 16px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer-nav a:hover {
    opacity: 1;
}

.social-links {
    display: flex;
    gap: 16px;
}

.social-link {
    color: #ffffff;
    text-decoration: none;
    font-size: 20px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.social-link:hover {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 48px;
    }

    .section-title {
        font-size: 36px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .nav-menu {
        display: none;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .pricing-card.featured {
        transform: none;
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-nav {
        order: -1;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 36px;
    }

    .section-title {
        font-size: 28px;
    }

    .hero-subtitle {
        font-size: 16px;
    }

    .section-subtitle {
        font-size: 16px;
    }

    .btn {
        padding: 14px 28px;
        font-size: 14px;
    }
}