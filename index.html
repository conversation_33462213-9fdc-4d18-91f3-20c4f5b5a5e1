<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BakerSTREET - Master Bread Making</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-icon">🌾</span>
                <span class="logo-text">BakerSTREET</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#about">About</a></li>
                <li><a href="#services">Services</a></li>
                <li><a href="#contacts">Contacts</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1 class="hero-title">
                Master<br>
                <em>Bread making</em>
            </h1>
            <p class="hero-subtitle">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Eu pretibus<br>
                pellentesque odio consequat ipsum dignissim massa gravida. Porttitor<br>
                venenatis velit praesent.
            </p>
            <div class="hero-buttons">
                <button class="btn btn-primary">Get started</button>
                <button class="btn btn-secondary">
                    <span class="play-icon">▶</span>
                    Watch trailer
                </button>
            </div>
        </div>
        <div class="scroll-indicator">
            <div class="scroll-arrow">⌄</div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <h2 class="section-title">
                A nunc, aliquam condimentum<br>
                <em>volutpat interdum arcu</em>
            </h2>
            <p class="section-subtitle">
                Create screens directly in Method or add your images from Sketch or Figma.<br>
                You can even sync designs from your cloud storage!
            </p>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-image"></div>
                    <h3>Videos lessons</h3>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                    <a href="#" class="read-more">Read More →</a>
                </div>

                <div class="feature-card">
                    <div class="feature-image"></div>
                    <h3>Slack community</h3>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                    <a href="#" class="read-more">Read More →</a>
                </div>

                <div class="feature-card">
                    <div class="feature-image"></div>
                    <h3>PDF resources</h3>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                    <a href="#" class="read-more">Read More →</a>
                </div>
            </div>

            <div class="explore-all">
                <button class="btn btn-outline">Explore All →</button>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing">
        <div class="container">
            <h2 class="section-title">
                Pricing <em>Options</em>
            </h2>
            <p class="section-subtitle">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit.<br>
                Sed dolor urna, tincidunt sed.
            </p>

            <div class="pricing-toggle">
                <span>Monthly</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="pricing-toggle">
                    <span class="slider"></span>
                </label>
                <span>Yearly</span>
            </div>

            <div class="pricing-grid">
                <div class="pricing-card">
                    <h3>Beginner</h3>
                    <div class="price">
                        <span class="currency">$</span>
                        <span class="amount">9</span>
                        <span class="period">per month</span>
                    </div>
                    <ul class="features-list">
                        <li>✓ 10 videos</li>
                        <li>✓ Limited access</li>
                        <li>✓ Up to 5</li>
                        <li>✓ Unlimited</li>
                    </ul>
                    <button class="btn btn-outline">Get Started</button>
                </div>

                <div class="pricing-card featured">
                    <h3>Intermediate</h3>
                    <div class="price">
                        <span class="currency">$</span>
                        <span class="amount">29</span>
                        <span class="period">per month</span>
                    </div>
                    <ul class="features-list">
                        <li>✓ Unlimited videos</li>
                        <li>✓ Unlimited access</li>
                        <li>✓ Up to 10 calls</li>
                    </ul>
                    <button class="btn btn-primary">Get Started</button>
                </div>

                <div class="pricing-card">
                    <h3>Pro</h3>
                    <div class="price">
                        <span class="currency">$</span>
                        <span class="amount">59</span>
                        <span class="period">per month</span>
                    </div>
                    <ul class="features-list">
                        <li>✓ Unlimited videos</li>
                        <li>✓ Unlimited access</li>
                        <li>✓ Unlimited Calls</li>
                    </ul>
                    <button class="btn btn-outline">Get Started</button>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq">
        <div class="container">
            <h2 class="section-title">
                Frequently Asked <em>Questions</em>
            </h2>
            <p class="section-subtitle">
                Nunc semper risus nam risus neque lacus quis aliquam. Pulvinar dignissim id<br>
                sem habitant. Mi, sed proin non venenatis nam.
            </p>

            <div class="faq-grid">
                <div class="faq-column">
                    <div class="faq-item">
                        <h4>Cras quis nulla commodo, aliquam lectus sed, blandit augue?</h4>
                        <p>Cras quis nulla commodo, aliquam lectus sed, blandit augue. Cras ullamcorper bibendum
                            bibendum. Duis tincidunt urna non pretium porta. Nam condimentum vitae ligula.</p>
                    </div>

                    <div class="faq-item">
                        <h4>Cras quis nulla commodo, aliquam lectus sed, blandit augue?</h4>
                        <p>Cras quis nulla commodo, aliquam lectus sed, blandit augue. Cras ullamcorper bibendum
                            bibendum. Duis tincidunt urna non pretium porta. Nam condimentum vitae ligula.</p>
                    </div>
                </div>

                <div class="faq-column">
                    <div class="faq-item">
                        <h4>Cras quis nulla commodo, aliquam lectus sed, blandit augue?</h4>
                        <p>Cras quis nulla commodo, aliquam lectus sed, blandit augue. Cras ullamcorper bibendum
                            bibendum. Duis tincidunt urna non pretium porta. Nam condimentum vitae ligula.</p>
                    </div>

                    <div class="faq-item">
                        <h4>Cras quis nulla commodo, aliquam lectus sed, blandit augue?</h4>
                        <p>Cras quis nulla commodo, aliquam lectus sed, blandit augue. Cras ullamcorper bibendum
                            bibendum. Duis tincidunt urna non pretium porta. Nam condimentum vitae ligula.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <span class="logo-icon">🌾</span>
                    <span class="logo-text">BakerSTREET</span>
                </div>
                <nav class="footer-nav">
                    <a href="#about">About</a>
                    <a href="#courses">Courses</a>
                    <a href="#services">Services</a>
                    <a href="#contacts">Contacts</a>
                </nav>
                <div class="social-links">
                    <a href="#" class="social-link">🐦</a>
                    <a href="#" class="social-link">💼</a>
                    <a href="#" class="social-link">🌐</a>
                </div>
            </div>
        </div>
    </footer>
</body>

</html>